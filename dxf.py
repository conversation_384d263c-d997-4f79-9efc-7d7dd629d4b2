import ezdxf
import os

def extract_dimension_values_from_dxf(file_path):
    """
    从DXF文件中提取所有标注（DIMENSION）的值。

    Args:
        file_path (str): DXF文件的路径。

    Returns:
        list: 包含所有标注文本值的列表。
    """
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在。")
        return []

    try:
        # 加载DXF文件
        doc = ezdxf.readfile(file_path)
    except IOError:
        print(f"错误: 无法读取文件 '{file_path}'。")
        return []
    except ezdxf.DXFStructureError:
        print(f"错误: '{file_path}' 文件结构无效或已损坏。")
        return []

    # 获取模型空间
    msp = doc.modelspace()

    # 存储标注值的列表
    dimension_values = []

    # 查询模型空间中所有的DIMENSION实体
    # ezdxf uses a powerful query language. 'DIMENSION' is the entity type we are looking for.
    for dim in msp.query('DIMENSION'):
        # 'text' 属性通常包含标注的显示文本。
        # '<>' 在DXF中代表默认的测量值。如果text是'<>'，
        # ezdxf 会自动计算实际的测量值。
        # dimension.get_measurement() 方法可以获取不带任何格式化或用户文本的原始测量值
        
        measured_value = dim.get_measurement()
        
        # dim.dxf.text 包含了用户定义的文本和/或测量值
        # 例如，用户可能输入了 "R<>"，其中<>会被实际半径替换
        dimension_text = dim.dxf.get('text', default='<>')

        dimension_values.append({
            "text": dimension_text,
            "measured_value": measured_value,
            "layer": dim.dxf.layer
        })

    return dimension_values

if __name__ == '__main__':
    # 将 "your_drawing.dxf" 替换为您的DXF文件路径
    dxf_file = "dxf.DXF" 
    
    # 创建一个简单的用于测试的DXF文件
    doc = ezdxf.new()
    msp = doc.modelspace()
    msp.add_linear_dim(base=(3, 2, 0), p1=(0, 0, 0), p2=(3, 0, 0)).render()
    msp.add_radius_dim(center=(0, 5, 0), radius=2, angle=45).render()
    doc.saveas(dxf_file)
    
    dimensions = extract_dimension_values_from_dxf(dxf_file)

    if dimensions:
        print(f"在文件 '{dxf_file}' 中找到 {len(dimensions)} 个标注:")
        for i, dim_data in enumerate(dimensions, 1):
            print(f"  {i}. 标注文本: '{dim_data['text']}', 测量值: {dim_data['measured_value']:.2f}, 图层: '{dim_data['layer']}'")

    # 清理创建的测试文件
    os.remove(dxf_file)