#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR功能测试脚本
用于测试不同OCR方案的可用性
"""

def test_pil():
    """测试PIL是否可用"""
    try:
        from PIL import Image
        print("✓ PIL (Pillow) 可用")
        return True
    except ImportError:
        print("✗ PIL (Pillow) 不可用 - 请运行: pip install pillow")
        return False

def test_easyocr():
    """测试EasyOCR是否可用"""
    try:
        import easyocr
        print("✓ EasyOCR 可用")
        return True
    except ImportError:
        print("✗ EasyOCR 不可用 - 请运行: pip install easyocr")
        return False

def test_tesseract():
    """测试Tesseract OCR是否可用"""
    try:
        import pytesseract
        print("✓ Tesseract OCR 可用")
        return True
    except ImportError:
        print("✗ Tesseract OCR 不可用 - 请运行: pip install pytesseract")
        return False

def test_opencv():
    """测试OpenCV是否可用"""
    try:
        import cv2
        print("✓ OpenCV 可用")
        return True
    except ImportError:
        print("✗ OpenCV 不可用 - 请运行: pip install opencv-python")
        return False

def main():
    print("检测OCR相关库的安装状态:")
    print("="*50)
    
    pil_ok = test_pil()
    easyocr_ok = test_easyocr()
    tesseract_ok = test_tesseract()
    opencv_ok = test_opencv()
    
    print("\n" + "="*50)
    print("功能状态总结:")
    print("="*50)
    
    if pil_ok or opencv_ok:
        print("✓ 图像处理: 可用")
    else:
        print("✗ 图像处理: 不可用")
    
    if easyocr_ok or tesseract_ok:
        print("✓ OCR文字识别: 可用")
    else:
        print("✗ OCR文字识别: 不可用 (将使用简单方案)")
    
    print("\n推荐安装顺序:")
    if not pil_ok:
        print("1. pip install pillow")
    if not easyocr_ok:
        print("2. pip install easyocr")
    if not opencv_ok:
        print("3. pip install opencv-python (可选)")
    
    print("\n安装完成后，重新运行 test.py 即可获得完整功能")

if __name__ == "__main__":
    main()
